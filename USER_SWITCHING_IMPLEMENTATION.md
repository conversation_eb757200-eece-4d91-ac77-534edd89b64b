# User Switching Implementation Summary

## Overview
This document summarizes the comprehensive user switching functionality implemented for the React application. The solution allows users to switch between different accounts while preserving the ability to return to their main account.

## Problem Solved
**Original Issue**: When switching users, the main user data was being overwritten in localStorage, making it impossible to switch back to the original account.

**Solution**: Implemented a separate storage system that maintains both the main user (original login) and current active user data, with proper state management through React Context.

## Key Components Implemented

### 1. UserContext Provider (`src/helpers/context/UserContext.jsx`)
- **Purpose**: Central state management for user switching functionality
- **Key States**:
  - `mainUser`: Original login user (never lost)
  - `currentActiveUser`: Currently displayed user
  - `isSwitchedUser`: Boolean flag indicating switch state
  - `availableUsers`: List of users available for switching

- **Key Functions**:
  - `handleUserLogin(userData)`: Sets main user on login
  - `switchToUser(switchedUserData, switchUserInfo)`: Switches to another user
  - `switchBackToMainUser()`: Returns to main user
  - `handleUserLogout()`: Clears all user data
  - `getUserDisplayData()`: Returns current active user for UI display

### 2. Storage Structure Updates (`src/helpers/constant/siteConstant.js`)
- Added `MAIN_USER_DATA: "mainUserData"` identifier
- Maintains separation between main user and current active user storage
- Preserves existing `USERDATA` identifier for current active user

### 3. Application Integration (`src/App.jsx`)
- Wrapped entire application with `UserProvider`
- Ensures user context is available throughout the app

### 4. Login Component Updates (`src/views/components/auth/login/index.jsx`)
- Updated to use `handleUserLogin` from user context
- Properly initializes main user data on login

### 5. Sidebar Component Enhancements (`src/layouts/sidebar/index.jsx`)
- **User Switching Logic**: Updated to use context-based switching
- **Visual Indicators**: 
  - Shows "(Switched)" indicator when user is switched
  - Highlights current active user in dropdown
- **Switch Back Functionality**: 
  - Added "Switch Back to Main User" option in dropdown
  - Only visible when user is currently switched
  - Green styling to indicate return to main account
- **Logout Integration**: Uses context logout function

## User Interface Features

### User Dropdown Menu
1. **Current User Display**: Shows active user with switch indicator
2. **Available Users List**: Shows all users available for switching
3. **Switch Back Option**: Prominent option to return to main user (only when switched)
4. **Add Other Account**: Existing functionality preserved

### Visual Indicators
- **(Switched)** text appears next to username in navbar when switched
- Green-styled "Switch Back" option in dropdown menu
- User avatar and name clearly displayed for each option

## Storage Management

### localStorage Structure
```javascript
// Main user (original login) - never overwritten
"mainUserData": { id, name, email, token, profile_image, ... }

// Current active user (changes when switching)
"userData": { id, name, email, token, profile_image, ... }

// Switch user metadata (when switched)
"switchUserData": { user_id, name, email, username, ... }
```

### Data Flow
1. **Login**: Main user and current user are the same
2. **Switch**: Current user changes, main user preserved
3. **Switch Back**: Current user restored to main user
4. **Logout**: All user data cleared

## API Integration
- **User Fetching**: `URL.All_USERS` and `URL.GET_INVITEE_USERS`
- **Switching Logic**: Maintains existing API calls
- **Authentication**: Token management handled automatically

## Testing
- Created comprehensive test suite (`src/test/UserSwitchingTest.jsx`)
- Test route available at `/test-user-switching`
- Tests all core functionality:
  - User login
  - User switching
  - Switch back to main user
  - Available users management
  - User logout

## Benefits of Implementation

### 1. Data Integrity
- Main user data never lost during switching
- Consistent state management across application
- Proper cleanup on logout

### 2. User Experience
- Clear visual indicators of switch state
- Easy return to main account
- Seamless switching between accounts
- Preserved existing functionality

### 3. Developer Experience
- Centralized user state management
- Easy to extend and maintain
- Comprehensive error handling
- Well-documented API

### 4. Scalability
- Supports unlimited user switching
- Efficient state management
- Memory-conscious implementation

## Usage Instructions

### For Users
1. **Login**: Use normal login process
2. **Switch User**: Click user dropdown → Select user from list
3. **Switch Back**: Click user dropdown → "← Back to [Name] (Main)"
4. **Logout**: Normal logout process

### For Developers
```javascript
// Access user context in any component
import { useUser } from '../helpers/context/UserContext';

const MyComponent = () => {
  const { 
    mainUser, 
    currentActiveUser, 
    isSwitchedUser,
    switchToUser,
    switchBackToMainUser 
  } = useUser();
  
  // Use getUserDisplayData() for UI display
  const displayUser = getUserDisplayData();
  
  return <div>{displayUser?.name}</div>;
};
```

## Future Enhancements
1. **Role-based switching**: Different permissions per switched user
2. **Switch history**: Track switching patterns
3. **Bulk operations**: Perform actions across multiple accounts
4. **Session management**: Handle token expiration during switches
5. **Audit logging**: Track user switching for security

## Files Modified
- `src/helpers/context/UserContext.jsx` (NEW)
- `src/helpers/constant/siteConstant.js`
- `src/App.jsx`
- `src/views/components/auth/login/index.jsx`
- `src/layouts/sidebar/index.jsx`
- `src/routes.jsx`
- `src/test/UserSwitchingTest.jsx` (NEW)

## Conclusion
The user switching implementation provides a robust, scalable solution that maintains data integrity while offering an intuitive user experience. The centralized context-based approach ensures consistency across the application and makes future enhancements straightforward to implement.
