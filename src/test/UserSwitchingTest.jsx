import React, { useState } from 'react';
import { useUser } from '../helpers/context/UserContext';
import { <PERSON><PERSON>, <PERSON>, CardContent, Typography, Box, Alert } from '@mui/material';

/**
 * Test component to verify user switching functionality
 * This component provides a UI to test all user switching features
 */
const UserSwitchingTest = () => {
  const {
    mainUser,
    currentActiveUser,
    isSwitchedUser,
    availableUsers,
    switchToUser,
    switchBackToMainUser,
    handleUserLogin,
    handleUserLogout,
    getUserDisplayData,
    updateAvailableUsers
  } = useUser();

  const [testResults, setTestResults] = useState([]);

  // Mock user data for testing
  const mockMainUser = {
    id: 1,
    name: "<PERSON>",
    email: "<EMAIL>",
    token: "main-user-token-123",
    profile_image: "https://via.placeholder.com/150"
  };

  const mockSwitchUser = {
    id: 2,
    name: "<PERSON>", 
    email: "<EMAIL>",
    token: "switch-user-token-456",
    profile_image: "https://via.placeholder.com/150"
  };

  const mockAvailableUsers = [
    {
      id: 2,
      name: "<PERSON>",
      email: "<EMAIL>",
      username: "janesmith",
      user_id: 2
    },
    {
      id: 3,
      name: "Bob <PERSON>",
      email: "<EMAIL>", 
      username: "bobjohnson",
      user_id: 3
    }
  ];

  const addTestResult = (test, result, expected, actual) => {
    const success = result === expected;
    setTestResults(prev => [...prev, {
      test,
      success,
      expected,
      actual,
      timestamp: new Date().toLocaleTimeString()
    }]);
  };

  // Test functions
  const testUserLogin = () => {
    handleUserLogin(mockMainUser);
    const displayData = getUserDisplayData();
    addTestResult(
      'User Login',
      displayData?.id === mockMainUser.id && !isSwitchedUser,
      true,
      displayData?.id === mockMainUser.id && !isSwitchedUser
    );
  };

  const testUserSwitch = () => {
    if (!mainUser) {
      addTestResult('User Switch', false, true, 'No main user logged in');
      return;
    }
    
    switchToUser(mockSwitchUser, mockAvailableUsers[0]);
    const displayData = getUserDisplayData();
    addTestResult(
      'User Switch',
      displayData?.id === mockSwitchUser.id && isSwitchedUser,
      true,
      displayData?.id === mockSwitchUser.id && isSwitchedUser
    );
  };

  const testSwitchBack = () => {
    if (!isSwitchedUser) {
      addTestResult('Switch Back', false, true, 'User not currently switched');
      return;
    }
    
    switchBackToMainUser();
    const displayData = getUserDisplayData();
    addTestResult(
      'Switch Back',
      displayData?.id === mainUser?.id && !isSwitchedUser,
      true,
      displayData?.id === mainUser?.id && !isSwitchedUser
    );
  };

  const testAvailableUsersUpdate = () => {
    updateAvailableUsers(mockAvailableUsers);
    addTestResult(
      'Available Users Update',
      availableUsers.length === mockAvailableUsers.length,
      true,
      availableUsers.length === mockAvailableUsers.length
    );
  };

  const testUserLogout = () => {
    handleUserLogout();
    const displayData = getUserDisplayData();
    addTestResult(
      'User Logout',
      !displayData && !mainUser && !isSwitchedUser,
      true,
      !displayData && !mainUser && !isSwitchedUser
    );
  };

  const runAllTests = () => {
    setTestResults([]);
    
    // Run tests in sequence
    setTimeout(() => testUserLogin(), 100);
    setTimeout(() => testAvailableUsersUpdate(), 200);
    setTimeout(() => testUserSwitch(), 300);
    setTimeout(() => testSwitchBack(), 400);
    setTimeout(() => testUserLogout(), 500);
  };

  const clearTests = () => {
    setTestResults([]);
  };

  return (
    <Box sx={{ p: 3, maxWidth: 800, margin: '0 auto' }}>
      <Typography variant="h4" gutterBottom>
        User Switching Test Suite
      </Typography>
      
      {/* Current State Display */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>Current State</Typography>
          <Typography><strong>Main User:</strong> {mainUser?.name || 'None'}</Typography>
          <Typography><strong>Current Active User:</strong> {currentActiveUser?.name || 'None'}</Typography>
          <Typography><strong>Is Switched:</strong> {isSwitchedUser ? 'Yes' : 'No'}</Typography>
          <Typography><strong>Available Users:</strong> {availableUsers.length}</Typography>
          <Typography><strong>Display Data:</strong> {getUserDisplayData()?.name || 'None'}</Typography>
        </CardContent>
      </Card>

      {/* Test Controls */}
      <Box sx={{ mb: 3, display: 'flex', gap: 2, flexWrap: 'wrap' }}>
        <Button variant="contained" onClick={testUserLogin}>
          Test Login
        </Button>
        <Button variant="contained" onClick={testUserSwitch} disabled={!mainUser}>
          Test Switch
        </Button>
        <Button variant="contained" onClick={testSwitchBack} disabled={!isSwitchedUser}>
          Test Switch Back
        </Button>
        <Button variant="contained" onClick={testAvailableUsersUpdate}>
          Test Available Users
        </Button>
        <Button variant="contained" onClick={testUserLogout}>
          Test Logout
        </Button>
        <Button variant="outlined" onClick={runAllTests}>
          Run All Tests
        </Button>
        <Button variant="outlined" onClick={clearTests}>
          Clear Results
        </Button>
      </Box>

      {/* Test Results */}
      {testResults.length > 0 && (
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>Test Results</Typography>
            {testResults.map((result, index) => (
              <Alert 
                key={index} 
                severity={result.success ? 'success' : 'error'}
                sx={{ mb: 1 }}
              >
                <strong>{result.test}</strong> - {result.success ? 'PASSED' : 'FAILED'}
                <br />
                <small>
                  Expected: {String(result.expected)} | 
                  Actual: {String(result.actual)} | 
                  Time: {result.timestamp}
                </small>
              </Alert>
            ))}
          </CardContent>
        </Card>
      )}
    </Box>
  );
};

export default UserSwitchingTest;
