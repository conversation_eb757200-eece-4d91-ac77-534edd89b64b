import React, { createContext, useContext, useState, useEffect } from 'react';
import { fetchFromStorage, saveToStorage } from './storage';
import siteConstant from '../constant/siteConstant';

// Create User Context
const UserContext = createContext();

// Custom hook to use User Context
export const useUser = () => {
  const context = useContext(UserContext);
  if (!context) {
    throw new Error('useUser must be used within a UserProvider');
  }
  return context;
};

// User Provider Component
export const UserProvider = ({ children }) => {
  // State for main user (original login user)
  const [mainUser, setMainUser] = useState(null);
  
  // State for current active user (could be main user or switched user)
  const [currentActiveUser, setCurrentActiveUser] = useState(null);
  
  // State to track if user is switched
  const [isSwitchedUser, setIsSwitchedUser] = useState(false);
  
  // State for available users to switch to
  const [availableUsers, setAvailableUsers] = useState([]);
  
  // Loading states
  const [isLoading, setIsLoading] = useState(true);

  // Initialize user data from localStorage on component mount
  useEffect(() => {
    initializeUserData();
  }, []);

  const initializeUserData = () => {
    try {
      // Get main user data (original login user)
      const storedMainUser = fetchFromStorage(siteConstant.INDENTIFIERS.MAIN_USER_DATA);
      
      // Get current active user data
      const storedCurrentUser = fetchFromStorage(siteConstant.INDENTIFIERS.USERDATA);
      
      // Get switch user data to check if user is switched
      const switchUserData = fetchFromStorage(siteConstant.INDENTIFIERS.SWITCH_USER_DATA);

      if (storedMainUser) {
        setMainUser(storedMainUser);
      } else if (storedCurrentUser) {
        // If no main user data exists but current user exists, 
        // it means this is the first login, so current user is main user
        setMainUser(storedCurrentUser);
        saveToStorage(siteConstant.INDENTIFIERS.MAIN_USER_DATA, storedCurrentUser);
      }

      if (storedCurrentUser) {
        setCurrentActiveUser(storedCurrentUser);
      }

      // Check if user is currently switched
      if (switchUserData && storedCurrentUser && storedMainUser) {
        const isCurrentlySwitched = storedCurrentUser.user_id !== storedMainUser.user_id;
        setIsSwitchedUser(isCurrentlySwitched);
      }

    } catch (error) {
      console.error('Error initializing user data:', error);
    } finally {
      setIsLoading(false);
    }
  };

  // Function to handle user login (sets main user)
  const handleUserLogin = (userData) => {
    setMainUser(userData);
    setCurrentActiveUser(userData);
    setIsSwitchedUser(false);
    
    // Save to localStorage
    saveToStorage(siteConstant.INDENTIFIERS.MAIN_USER_DATA, userData);
    saveToStorage(siteConstant.INDENTIFIERS.USERDATA, userData);
    
    // Clear any existing switch user data
    localStorage.removeItem(siteConstant.INDENTIFIERS.SWITCH_USER_DATA);
  };

  // Function to switch to another user
  const switchToUser = (switchedUserData, switchUserInfo) => {
    setCurrentActiveUser(switchedUserData);
    setIsSwitchedUser(true);
    
    // Save to localStorage
    saveToStorage(siteConstant.INDENTIFIERS.USERDATA, switchedUserData);
    saveToStorage(siteConstant.INDENTIFIERS.SWITCH_USER_DATA, switchUserInfo);
  };

  // Function to switch back to main user
  const switchBackToMainUser = () => {
    if (mainUser) {
      setCurrentActiveUser(mainUser);
      setIsSwitchedUser(false);
      
      // Update localStorage
      saveToStorage(siteConstant.INDENTIFIERS.USERDATA, mainUser);
      localStorage.removeItem(siteConstant.INDENTIFIERS.SWITCH_USER_DATA);
    }
  };

  // Function to handle user logout
  const handleUserLogout = () => {
    setMainUser(null);
    setCurrentActiveUser(null);
    setIsSwitchedUser(false);
    setAvailableUsers([]);
    
    // Clear localStorage
    localStorage.removeItem(siteConstant.INDENTIFIERS.MAIN_USER_DATA);
    localStorage.removeItem(siteConstant.INDENTIFIERS.USERDATA);
    localStorage.removeItem(siteConstant.INDENTIFIERS.SWITCH_USER_DATA);
  };

  // Function to update available users for switching
  const updateAvailableUsers = (users) => {
    setAvailableUsers(users);
  };

  // Function to get user display data (for UI components)
  const getUserDisplayData = () => {
    return currentActiveUser || mainUser;
  };

  // Function to check if current user has admin privileges
  const hasAdminPrivileges = () => {
    const user = getUserDisplayData();
    return user?.is_admin || false;
  };

  // Function to get user token
  const getUserToken = () => {
    const user = getUserDisplayData();
    return user?.token;
  };

  const contextValue = {
    // User data
    mainUser,
    currentActiveUser,
    isSwitchedUser,
    availableUsers,
    isLoading,
    
    // Functions
    handleUserLogin,
    switchToUser,
    switchBackToMainUser,
    handleUserLogout,
    updateAvailableUsers,
    getUserDisplayData,
    hasAdminPrivileges,
    getUserToken,
    initializeUserData,
  };

  return (
    <UserContext.Provider value={contextValue}>
      {children}
    </UserContext.Provider>
  );
};

export default UserContext;
