import React from "react";
import { Navigate } from "react-router-dom";
import Sidebar from "./layouts/sidebar";
import AuthLayout from "./layouts/auth_layout/index.jsx";
import SidebarLayout from "./layouts/privacy_layout/index.jsx";
// USER
const Home = React.lazy(() => import("./views/pages/dashboard/home.jsx"));
const PostFeed = React.lazy(() => import("./views/pages/feed/index.jsx"));
const Subscription = React.lazy(() =>
  import("./views/common/subscription.jsx")
);
const UserManage = React.lazy(() =>
  import("./views/pages/usermanagement/usermanagement.jsx")
);

const ScheduledPost = React.lazy(() =>
  import("./views/pages/scheduledpost/scheduledpost.jsx")
);
const One = React.lazy(() => import("./views/components/chat/Index.jsx"));
const Analytics = React.lazy(() =>
  import("./views/components/analytics/index.jsx")
);
// const Dashboard = React.lazy(() =>
//   import("./views/admin/pages/dashboard/index.jsx")
// );

const PrivacyPolicy = React.lazy(() =>
  import("./views/pages/Privacy-Terms/privacy-policy.jsx")
);

const TermsCondition = React.lazy(() => import("./views/pages/Term/terms.jsx"));

// const PostDetails = React.lazy(() =>
//   import("./views/admin/pages/postDetails/index.jsx")
// );
const ContactUs = React.lazy(() =>
  import("./views/pages/contactus/contactus.jsx")
);

// const UserManagement = React.lazy(() =>
//   import("./views/admin/pages/user-management/index.jsx")
// );
const Profile = React.lazy(() => import("./views/pages/profile/profile.jsx"));
const SinglePost = React.lazy(() =>
  import("./views/components/profile/SiglePost.jsx")
);
const ShareProfile = React.lazy(() =>
  import("./views/pages/shareprofile/shareprofile.jsx")
);
const SignIn = React.lazy(() => import("./views/pages/auth/sign-in.jsx"));
const SignUp = React.lazy(() => import("./views/pages/auth/sign-up.jsx"));
const Forgot = React.lazy(() =>
  import("./views/pages/auth/forgot-password.jsx")
);
const Reset = React.lazy(() => import("./views/pages/auth/reset-password.jsx"));
const Feedback = React.lazy(() =>
  import("./views/components/feedback/index.jsx")
);
const Planner = React.lazy(() =>
  import("./views/components/Planner/index.jsx")
);
const Brands = React.lazy(() => import("./views/pages/brands/brands.jsx"));
const Live = React.lazy(() => import("./views/pages/live_stream/live.jsx"));

// Standalone live stream viewer component
const LiveStreamViewer = React.lazy(() =>
  import("./views/components/live-stream/LiveStreamViewer.jsx")
);

// Comprehensive live stream data component for backend integration
const LiveStreamData = React.lazy(() =>
  import("./views/components/live-stream/LiveStreamData.jsx")
);

const RoleManagement = React.lazy(() =>
  import("./views/pages/Role-Mangement/role-manage.jsx")
);

const TypeSelection = React.lazy(() =>
  import("./views/pages/auth/type-selection.jsx")
);
const OtpVerification = React.lazy(() =>
  import("./views/pages/auth/otp-verify.jsx")
);
const DataNotFound = React.lazy(() =>
  import("./views/components/custom/DataNotFound.jsx")
);
const InstaLogin = React.lazy(() =>
  import("./views/pages/auth/instagram/login.jsx")
);
const InstaResponse = React.lazy(() =>
  import("./views/pages/auth/instagram/response")
);
const NotFoundPage = React.lazy(() => import("./views/pages/exception"));
const BadGatewayPage = React.lazy(() =>
  import("./views/pages/exception/BadGatewayPage.jsx")
);
const SignInn = React.lazy(() =>
  import("./views/components/switch-user/sign_in/sign_in.jsx")
);
const UserSwitchingTest = React.lazy(() =>
  import("./test/UserSwitchingTest.jsx")
);

const instaAuthRoutes = {
  path: "/oauth/instagram",
  children: [
    { path: "/oauth/instagram", element: <InstaLogin /> },
    { path: "/oauth/instagram/response", element: <InstaResponse /> },
  ],
};

const publicOther = [
  {
    path: "*",
    element: <NotFoundPage />,
  },
  {
    path: "/contactus",
    element: <ContactUs />,
  },
  {
    path: "/",
    element: <SidebarLayout />,
    children: [
      { path: "/privacy-policy", element: <PrivacyPolicy /> },
      { path: "/terms-of-use", element: <TermsCondition /> },
      { path: "/contact-us", element: <ContactUs /> },
    ],
  },
  // Standalone live stream viewer route
  {
    path: "/live/:streamId",
    element: <LiveStreamViewer />,
  },
  // Comprehensive live stream data route for backend integration
  {
    path: "/stream-data/:streamId",
    element: <LiveStreamData />,
  },
  // Universal live stream route that works on any platform
  {
    path: "/u/:streamId",
    element: <LiveStreamViewer />,
  },
  // Alternative universal route for better compatibility
  {
    path: "/watch/:streamId",
    element: <LiveStreamViewer />,
  },
];

const userRoutes = [
  {
    path: "/",
    element: <Sidebar />,
    children: [
      { path: "/", element: <Navigate to="/dashboard" /> },
      { path: "/dashboard", element: <Home /> },
      { path: "/scheduled-post", element: <ScheduledPost /> },
      { path: "/chat", element: <One /> },
      { path: "/analytics", element: <Analytics /> },
      { path: "/feed", element: <PostFeed /> },
      { path: "/profile", element: <Profile /> },
      { path: "/post/:id", element: <SinglePost /> },
      { path: "/subscription", element: <Subscription /> },
      { path: "/UserManagement", element: <UserManage /> },
      { path: "/feedback", element: <Feedback /> },
      { path: "/planner", element: <Planner /> },
      { path: "/plateforms", element: <Brands /> },
      { path: "/live", element: <Live /> },
      { path: "/SignInn", element: <SignInn /> },
      { path: "/test-user-switching", element: <UserSwitchingTest /> },

      { path: "/role-manage", element: <RoleManagement /> },

      { path: "/view-profile/", element: <ShareProfile /> },
      { path: "/404", element: <DataNotFound /> },
      { path: "/bad-gateway", element: <BadGatewayPage /> },
    ],
  },
  {
    path: "/view-profile/",
    element: <ShareProfile />,
  },
  instaAuthRoutes,
  ...publicOther,
];

// const adminRoutes = [
//   // {
//   //   path: "/admin",
//   //   element: <Sidebar />,
//   //   children: [
//   //     { path: "/admin", element: <Navigate to="/admin/user-management" /> },
//   //     { path: "/admin/dashboard", element: <Dashboard /> },
//   //     { path: "/admin/post-details", element: <PostDetails /> },
//   //     { path: "/admin/user-management", element: <UserManagement /> },
//   //     { path: "/admin/404", element: <DataNotFound /> },
//   //   ],
//   // },
//   {
//     path: "/view-profile/",
//     element: <ShareProfile />,
//   },
//   instaAuthRoutes,
//   ...publicOther,
// ];

const publicRoute = [
  {
    path: "/",
    element: <AuthLayout />,
    children: [
      { path: "/", element: <Navigate to="/sign-in" /> },
      { path: "/sign-in", element: <SignIn /> },
      { path: "/type-selection", element: <TypeSelection /> },
      { path: "/forgot-password", element: <Forgot /> },
      { path: "/sign-up", element: <SignUp /> },
      { path: "/otp-verify", element: <OtpVerification /> },
      { path: "/reset-password", element: <Reset /> },
      { path: "/bad-gateway", element: <BadGatewayPage /> },
    ],
  },
  {
    path: "/bad-gateway",
    element: <BadGatewayPage />,
  },
  {
    path: "/view-profile/",
    element: <ShareProfile />,
  },
  instaAuthRoutes,
  ...publicOther,
];

export { publicRoute, userRoutes };
// adminRoutes
